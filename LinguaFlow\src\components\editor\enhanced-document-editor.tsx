"use client";

import React, { useState, useEffect, useCallback, useRef, useMemo, forwardRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useToast } from '@/hooks/use-toast';
import { useI18n } from '@/contexts/i18n-context';
import { useDebounce } from '@/hooks/use-debounce';
import { cn } from '@/lib/utils';
import { Check, X, AlertTriangle, ShieldCheck, Sparkles, Edit3 } from 'lucide-react';

// Removed type import - now defined locally
type AnalysisSuggestion = {
  id: string;
  type: 'style' | 'spelling' | 'grammar' | 'rewrite';
  message: string;
  suggestion: string;
  originalSegment: string;
  severity: 'low' | 'medium' | 'high';
  startIndex?: number;
  endIndex?: number;
  suggestions?: string[];
};

// Enhanced suggestion types with new categories
export interface EnhancedSuggestion extends AnalysisSuggestion {
  category: 'spelling' | 'grammar' | 'style' | 'vocabulary' | 'plagiarism' | 'similarity';
  confidence: number;
  replacements?: string[];
  explanation?: string;
  source?: string;
  context?: {
    before: string;
    after: string;
  };
}

// Enhanced document analysis interface
export interface DocumentAnalysis {
  suggestions: EnhancedSuggestion[];
  metrics: {
    readabilityScore: number;
    wordCount: number;
    sentenceCount: number;
    avgWordsPerSentence: number;
    complexWords: number;
    passiveVoice: number;
  };
  plagiarismCheck?: {
    similarity: number;
    sources: Array<{
      url: string;
      title: string;
      similarity: number;
      matchedText: string;
    }>;
  };
}

// Enhanced props interface
export interface EnhancedDocumentEditorProps {
  value: string;
  onChange: (value: string) => void;
  onAnalysisComplete?: (analysis: DocumentAnalysis) => void;
  placeholder?: string;
  disabled?: boolean;
  autoAnalyze?: boolean;
  analysisDelay?: number;
  showMetrics?: boolean;
  enablePlagiarismCheck?: boolean;
  enableGrammarCheck?: boolean;
  enableStyleSuggestions?: boolean;
  enableVocabularyEnhancement?: boolean;
  maxSuggestions?: number;
  language?: string;
  suggestions?: EnhancedSuggestion[];
  onApplySuggestion?: (suggestion: EnhancedSuggestion) => void;
  onDismissSuggestion?: (suggestionId: string) => void;
  isAnalyzing?: boolean;
  analysisProgress?: number;
  similaritySources?: Array<{
    url: string;
    title: string;
    similarity: number;
    matchedText: string;
  }>;
  writingMode?: 'formal' | 'casual' | 'academic' | 'creative';
  direction?: 'ltr' | 'rtl';
  onUndo?: () => void;
  onRedo?: () => void;
  canUndo?: boolean;
  canRedo?: boolean;
  className?: string;
}

// Enhanced Document Editor Component
const EnhancedDocumentEditor = forwardRef<HTMLTextAreaElement, EnhancedDocumentEditorProps>(({
    value,
    onChange,
    onAnalysisComplete,
    placeholder = "Start writing your document...",
    disabled = false,
    autoAnalyze = true,
    analysisDelay = 1000,
    showMetrics = true,
    enablePlagiarismCheck = false,
    enableGrammarCheck = true,
    enableStyleSuggestions = true,
    enableVocabularyEnhancement = true,
    maxSuggestions = 10,
    language = 'en',
    suggestions = [],
    isAnalyzing = false,
    analysisProgress = 0,
    similaritySources = [],
    onApplySuggestion,
    onDismissSuggestion,
    writingMode,
    direction,
    onUndo,
    onRedo,
    canUndo,
    canRedo,
    className,
  }, ref) => {
    const { t } = useI18n();
    const { toast } = useToast();
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const [selectedSuggestion, setSelectedSuggestion] = useState<EnhancedSuggestion | null>(null);
    const [popoverOpen, setPopoverOpen] = useState(false);
    const [cursorPosition, setCursorPosition] = useState(0);
    const [localSuggestions, setLocalSuggestions] = useState<EnhancedSuggestion[]>(suggestions);
    
    // Debounced value for analysis
    const debouncedValue = useDebounce(value, analysisDelay);
    
    // Mock analysis function - in real implementation, this would call your AI service
    const analyzeDocument = useCallback(async (text: string): Promise<DocumentAnalysis> => {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock analysis results
      const mockSuggestions: EnhancedSuggestion[] = [
        {
          id: '1',
          type: 'grammar',
          category: 'grammar',
          message: 'Consider using active voice',
          suggestion: 'The team completed the project',
          originalSegment: 'The project was completed by the team',
          severity: 'medium',
          confidence: 0.85,
          startIndex: 0,
          endIndex: 35,
          explanation: 'Active voice makes your writing more direct and engaging.'
        },
        {
          id: '2',
          type: 'style',
          category: 'vocabulary',
          message: 'Consider a more precise word',
          suggestion: 'excellent',
          originalSegment: 'good',
          severity: 'low',
          confidence: 0.75,
          startIndex: 50,
          endIndex: 54,
          replacements: ['excellent', 'outstanding', 'remarkable'],
          explanation: 'Using more specific adjectives can enhance your writing.'
        }
      ];
      
      return {
        suggestions: mockSuggestions,
        metrics: {
          readabilityScore: 75,
          wordCount: text.split(' ').length,
          sentenceCount: text.split('.').length - 1,
          avgWordsPerSentence: 15,
          complexWords: 5,
          passiveVoice: 2
        },
        plagiarismCheck: enablePlagiarismCheck ? {
          similarity: 15,
          sources: similaritySources
        } : undefined
      };
    }, [enablePlagiarismCheck, similaritySources]);
    
    // Auto-analyze when content changes
    useEffect(() => {
      if (autoAnalyze && debouncedValue.trim()) {
        analyzeDocument(debouncedValue)
          .then(analysis => {
            setLocalSuggestions(analysis.suggestions.slice(0, maxSuggestions));
            onAnalysisComplete?.(analysis);
          })
          .catch(error => {
            console.error('Analysis failed:', error);
            toast({
              title: t('analysis.error'),
              description: t('analysis.errorDescription'),
              variant: 'destructive'
            });
          });
      }
    }, [debouncedValue, autoAnalyze, analyzeDocument, maxSuggestions, onAnalysisComplete, t, toast]);
    
    // Handle suggestion application
    const handleApplySuggestion = useCallback((suggestion: EnhancedSuggestion) => {
      if (suggestion.startIndex !== undefined && suggestion.endIndex !== undefined) {
        const newValue = value.substring(0, suggestion.startIndex) + 
                         suggestion.suggestion + 
                         value.substring(suggestion.endIndex);
        onChange(newValue);
        onApplySuggestion?.(suggestion);
        
        // Remove applied suggestion
        setLocalSuggestions(prev => prev.filter(s => s.id !== suggestion.id));
        setPopoverOpen(false);
        
        toast({
          title: t('suggestion.applied'),
          description: t('suggestion.appliedDescription'),
        });
      }
    }, [value, onChange, onApplySuggestion, t, toast]);
    
    // Handle suggestion dismissal
    const handleDismissSuggestion = useCallback((suggestionId: string) => {
      setLocalSuggestions(prev => prev.filter(s => s.id !== suggestionId));
      onDismissSuggestion?.(suggestionId);
      setPopoverOpen(false);
      
      toast({
        title: t('suggestion.dismissed'),
        description: t('suggestion.dismissedDescription'),
      });
    }, [onDismissSuggestion, t, toast]);
    
    // Handle cursor position changes
    const handleSelectionChange = useCallback(() => {
      if (textareaRef.current) {
        setCursorPosition(textareaRef.current.selectionStart);
      }
    }, []);
    
    // Get suggestions for current cursor position
    const currentSuggestions = useMemo(() => {
      return localSuggestions.filter(suggestion => {
        if (suggestion.startIndex === undefined || suggestion.endIndex === undefined) return false;
        return cursorPosition >= suggestion.startIndex && cursorPosition <= suggestion.endIndex;
      });
    }, [localSuggestions, cursorPosition]);
    
    // Render suggestion severity icon
    const getSeverityIcon = (severity: string) => {
      switch (severity) {
        case 'high': return <AlertTriangle className="h-4 w-4 text-red-500" />;
        case 'medium': return <ShieldCheck className="h-4 w-4 text-yellow-500" />;
        case 'low': return <Check className="h-4 w-4 text-green-500" />;
        default: return <Sparkles className="h-4 w-4 text-blue-500" />;
      }
    };
    
    // Render category icon
    const getCategoryIcon = (category: string) => {
      switch (category) {
        case 'grammar': return <Edit3 className="h-4 w-4" />;
        case 'spelling': return <Check className="h-4 w-4" />;
        case 'style': return <Sparkles className="h-4 w-4" />;
        case 'vocabulary': return <ShieldCheck className="h-4 w-4" />;
        default: return <AlertTriangle className="h-4 w-4" />;
      }
    };

    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Analysis Progress */}
            {isAnalyzing && (
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                  style={{ width: `${analysisProgress}%` }}
                />
              </div>
            )}
            
            {/* Text Editor with Suggestions */}
            <div className="relative">
              <Popover open={popoverOpen && currentSuggestions.length > 0} onOpenChange={setPopoverOpen}>
                <PopoverTrigger asChild>
                  <Textarea
                    ref={textareaRef}
                    value={value}
                    onChange={(e) => onChange(e.target.value)}
                    onSelect={handleSelectionChange}
                    onKeyUp={handleSelectionChange}
                    onClick={handleSelectionChange}
                    placeholder={placeholder}
                    disabled={disabled}
                    className={cn(
                      "min-h-[300px] resize-none",
                      direction === 'rtl' && "text-right",
                      currentSuggestions.length > 0 && "border-blue-500"
                    )}
                    dir={direction}
                  />
                </PopoverTrigger>
                
                {/* Suggestions Popover */}
                <PopoverContent className="w-80 p-0" side="top" align="start">
                  <div className="max-h-60 overflow-y-auto">
                    {currentSuggestions.map((suggestion) => (
                      <div key={suggestion.id} className="p-3 border-b last:border-b-0">
                        <div className="flex items-start gap-2">
                          <div className="flex items-center gap-1">
                            {getSeverityIcon(suggestion.severity)}
                            {getCategoryIcon(suggestion.category)}
                          </div>
                          <div className="flex-1 space-y-2">
                            <p className="text-sm font-medium">{suggestion.message}</p>
                            <p className="text-xs text-gray-600">{suggestion.explanation}</p>
                            
                            {/* Original vs Suggested */}
                            <div className="space-y-1">
                              <div className="text-xs">
                                <span className="font-medium text-red-600">Original:</span>
                                <span className="ml-1 bg-red-50 px-1 rounded">{suggestion.originalSegment}</span>
                              </div>
                              <div className="text-xs">
                                <span className="font-medium text-green-600">Suggested:</span>
                                <span className="ml-1 bg-green-50 px-1 rounded">{suggestion.suggestion}</span>
                              </div>
                            </div>
                            
                            {/* Alternative suggestions */}
                            {suggestion.replacements && suggestion.replacements.length > 1 && (
                              <div className="text-xs">
                                <span className="font-medium">Alternatives:</span>
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {suggestion.replacements.slice(1).map((alt, index) => (
                                    <span key={index} className="bg-blue-50 px-1 rounded text-blue-700">
                                      {alt}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            )}
                            
                            {/* Action buttons */}
                            <div className="flex gap-2">
                              <Button
                                size="sm"
                                onClick={() => handleApplySuggestion(suggestion)}
                                className="h-6 px-2 text-xs"
                              >
                                <Check className="h-3 w-3 mr-1" />
                                Apply
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDismissSuggestion(suggestion.id)}
                                className="h-6 px-2 text-xs"
                              >
                                <X className="h-3 w-3 mr-1" />
                                Dismiss
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </PopoverContent>
              </Popover>
            </div>
            
            {/* Suggestions Summary */}
            {localSuggestions.length > 0 && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <AlertTriangle className="h-4 w-4" />
                <span>{localSuggestions.length} suggestions available</span>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setPopoverOpen(!popoverOpen)}
                  className="h-6 px-2 text-xs ml-auto"
                >
                  {popoverOpen ? 'Hide' : 'Show'} Suggestions
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
});

EnhancedDocumentEditor.displayName = 'EnhancedDocumentEditor';

export default EnhancedDocumentEditor;

import { useState, useEffect, useRef, useMemo, useCallback } from 'react';
"use client";

import React, { useState, useEffect, useCallback, useRef, useMemo, forwardRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useToast } from '@/hooks/use-toast';
import { useI18n } from '@/contexts/i18n-context';
import { useDebounce } from '@/hooks/use-debounce';
import { cn } from '@/lib/utils';
// Removed type import - now defined locally
type AnalysisSuggestion = {
  id: string;
  type: 'style' | 'spelling' | 'grammar' | 'rewrite';
  message: string;
  suggestion: string;
  originalSegment: string;
  severity: 'low' | 'medium' | 'high';
  startIndex?: number;
  endIndex?: number;
  suggestions?: string[];
};
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Shield<PERSON>he<PERSON>, Spa<PERSON>les, Edit3 } from 'lucide-react';

// Enhanced suggestion types with new categories
export interface EnhancedSuggestion extends AnalysisSuggestion {
  category: 'spelling' | 'grammar' | 'style' | 'vocabulary' | 'plagiarism' | 'similarity';
  confidence: number;
  severity: 'low' | 'medium' | 'high';
}

export interface PlagiarismSource extends AnalysisSuggestion {
  category: 'plagiarism';
  sourceUrl?: string;
  similarityScore: number;
  confidence: number;
  severity: 'low' | 'medium' | 'high';
}

export interface SimilaritySource extends AnalysisSuggestion {
  category: 'similarity';
  sourceUrl?: string;
  similarityScore: number;
  confidence: number;
  severity: 'low' | 'medium' | 'high';
}

interface SuggestionTooltipProps {
  suggestion: EnhancedSuggestion;
  onApply: (suggestion: EnhancedSuggestion) => void;
  onDismiss: (suggestionId: string) => void;
  onClose: () => void;
}

interface EnhancedDocumentEditorProps {
  value: string;
  onChange: (value: string) => void;
  suggestions: EnhancedSuggestion[];
  plagiarismSources: PlagiarismSource[];
  similaritySources: SimilaritySource[];
  onApplySuggestion: (suggestion: EnhancedSuggestion) => void;
  onDismissSuggestion: (suggestionId: string) => void;
  writingMode: string;
  direction: 'ltr' | 'rtl';
  onUndo: () => void;
  onRedo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  className?: string;
}

// Suggestion tooltip component with category-specific styling
function SuggestionTooltip({ suggestion, onApply, onDismiss, onClose }: SuggestionTooltipProps) {
  const { t } = useI18n();

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'spelling':
      case 'grammar':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'style':
        return <Edit3 className="h-4 w-4 text-blue-600" />;
      case 'vocabulary':
        return <Sparkles className="h-4 w-4 text-green-600" />;
      case 'plagiarism':
        return <ShieldCheck className="h-4 w-4 text-red-700" />;
      case 'similarity':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      default:
        return <Edit3 className="h-4 w-4 text-gray-600" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'spelling':
      case 'grammar':
        return 'border-red-200 bg-red-50';
      case 'style':
        return 'border-blue-200 bg-blue-50';
      case 'vocabulary':
        return 'border-green-200 bg-green-50';
      case 'plagiarism':
        return 'border-red-300 bg-red-100';
      case 'similarity':
        return 'border-yellow-200 bg-yellow-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <PopoverContent className={cn("w-80 p-4 border-2", getCategoryColor(suggestion.category))}>
      <div className="space-y-3">
        <div className="flex items-start gap-2">
          {getCategoryIcon(suggestion.category)}
          <div className="flex-1">
            <h4 className="font-medium text-sm capitalize">{suggestion.category} Suggestion</h4>
            <p className="text-sm text-muted-foreground mt-1">{suggestion.message}</p>
          </div>
        </div>
        
        {suggestion.suggestion && (
          <div className="p-3 bg-white rounded border">
            <p className="text-sm font-medium text-green-700">Suggested:</p>
            <p className="text-sm mt-1">{suggestion.suggestion}</p>
          </div>
        )}

        <div className="flex gap-2 pt-2">
          <Button 
            size="sm" 
            onClick={() => {
              onApply(suggestion);
              onClose();
            }}
            className="flex-1"
          >
            <Check className="h-3 w-3 mr-1" />
            {t('correctButton')}
          </Button>
          <Button 
            size="sm" 
            variant="outline" 
            onClick={() => {
              onDismiss(suggestion.id);
              onClose();
            }}
            className="flex-1"
          >
            <X className="h-3 w-3 mr-1" />
            {t('dismissButton')}
          </Button>
        </div>
      </div>
    </PopoverContent>
  );
}

export const EnhancedDocumentEditor = forwardRef<HTMLDivElement, EnhancedDocumentEditorProps>(
  ({
    value,
    onChange,
    suggestions,
    plagiarismSources,
    similaritySources,
    onApplySuggestion,
    onDismissSuggestion,
    writingMode,
    direction,
    onUndo,
    onRedo,
    canUndo,
    canRedo,
    className,
  }, ref) => {
    const { t } = useI18n();
    const [activeSuggestion, setActiveSuggestion] = useState<EnhancedSuggestion | null>(null);
    
    // Combine all suggestions for rendering
    const allSuggestions = useMemo(() => {
      return [...suggestions, ...plagiarismSources, ...similaritySources];
    }, [suggestions, plagiarismSources, similaritySources]);

    // Handle applying inline suggestions
    const handleApplyInlineSuggestion = useCallback((suggestion: EnhancedSuggestion) => {
      onApplySuggestion(suggestion);
    }, [onApplySuggestion]);

    // Get highlight class based on suggestion category
    const getHighlightClass = (suggestion: EnhancedSuggestion) => {
      const baseClass = "cursor-pointer transition-all duration-200 hover:opacity-80 relative";
      
      switch (suggestion.category) {
        case 'spelling':
        case 'grammar':
          return cn(baseClass, "bg-red-100 border-b-2 border-red-500 text-red-900");
        case 'style':
          return cn(baseClass, "bg-blue-100 border-b-2 border-blue-500 text-blue-900");
        case 'vocabulary':
          return cn(baseClass, "bg-green-100 border-b-2 border-green-500 text-green-900");
        case 'plagiarism':
          return cn(baseClass, "bg-red-200 border-b-2 border-red-600 text-red-950");
        case 'similarity':
          return cn(baseClass, "bg-yellow-100 border-b-2 border-yellow-500 text-yellow-900");
        default:
          return cn(baseClass, "bg-gray-100 border-b-2 border-gray-500 text-gray-900");
      }
    };

    // Handle highlight click
    const handleHighlightClick = (suggestion: EnhancedSuggestion) => {
      setActiveSuggestion(suggestion);
    };

    const closeSuggestionPopover = () => {
      setActiveSuggestion(null);
    };

    // Render highlighted content with suggestions
    const renderHighlightedContent = () => {
      if (!value) return <span>&nbsp;</span>;

      const lines = value.split('\n');
      const sortedSuggestions = [...allSuggestions].sort((a, b) => (a.startIndex || 0) - (b.startIndex || 0));
      
      let suggestionIndex = 0;
      let charIndex = 0;

      return lines.map((line, lineIndex) => {
        const lineStart = charIndex;
        const lineEnd = charIndex + line.length;
        const parts: React.ReactNode[] = [];
        let lastIndexInLine = 0;

        // Process suggestions that fall within this line
        while (suggestionIndex < sortedSuggestions.length) {
          const suggestion = sortedSuggestions[suggestionIndex];
          const sStart = (suggestion.startIndex || 0) - lineStart;
          const sEnd = (suggestion.endIndex || 0) - lineStart;

          // If suggestion starts after this line, break
          if (sStart >= line.length) break;
          
          // If suggestion ends before this line, skip
          if (sEnd <= 0) {
            suggestionIndex++;
            continue;
          }

          // Add text before suggestion
          if (sStart > lastIndexInLine) {
            parts.push(line.substring(lastIndexInLine, sStart));
          }

          // Add highlighted suggestion
          const highlightStart = Math.max(0, sStart);
          const highlightEnd = Math.min(line.length, sEnd);
          const highlightedText = line.substring(highlightStart, highlightEnd);

          parts.push(
            <Popover key={suggestion.id} open={activeSuggestion?.id === suggestion.id} onOpenChange={(open) => {if (!open) closeSuggestionPopover()}}>
              <PopoverTrigger asChild>
                <span 
                  className={getHighlightClass(suggestion)} 
                  onClick={() => handleHighlightClick(suggestion)}
                >
                  {highlightedText}
                </span>
              </PopoverTrigger>
              {activeSuggestion?.id === suggestion.id && (
                <SuggestionTooltip 
                  suggestion={suggestion}
                  onApply={handleApplyInlineSuggestion} 
                  onDismiss={onDismissSuggestion}
                  onClose={closeSuggestionPopover}
                />
              )}
            </Popover>
          );

          lastIndexInLine = highlightEnd;
          suggestionIndex++;
        }

        // Add remaining text in line
        if (lastIndexInLine < line.length) {
          parts.push(line.substring(lastIndexInLine));
        }

        charIndex = lineEnd + 1; // +1 for newline character

        return (
          <React.Fragment key={lineIndex}>
            {parts.length > 0 ? parts : <span>&nbsp;</span>}
            {lineIndex < lines.length - 1 && '\n'}
          </React.Fragment>
        );
      });
    };

    return (
      <Card ref={ref} className={cn("h-full flex flex-col", className)}>
        <CardContent className="flex-1 p-6 relative">
          <div className="relative h-full">
            {/* Main textarea */}
            <Textarea
              value={value}
              onChange={(e) => onChange(e.target.value)}
              placeholder={t('startWritingPlaceholder')}
              className="w-full h-full resize-none border-none bg-transparent text-transparent caret-black relative z-10"
              style={{ 
                direction,
                fontFamily: "'Inter', sans-serif",
                fontSize: '16px',
                lineHeight: '1.6',
              }}
            />
            
            {/* Backdrop with highlighted content */}
            <div 
              className="absolute inset-0 p-3 pointer-events-none whitespace-pre-wrap break-words overflow-hidden"
              style={{ 
                direction,
                fontFamily: "'Inter', sans-serif",
                fontSize: '16px',
                lineHeight: '1.6',
                color: 'rgb(15 23 42)', // slate-900
              }}
            >
              {renderHighlightedContent()}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }
);

EnhancedDocumentEditor.displayName = 'EnhancedDocumentEditor';
export default EnhancedDocumentEditor;  // Exported as default